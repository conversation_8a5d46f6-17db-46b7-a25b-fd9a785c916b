#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版知识检索模块
不依赖faiss，使用基础向量相似度计算
"""

import json
import os
import numpy as np
from typing import List, Dict, Any, Tuple, Optional

class KnowledgeRetriever:
    """简化版知识检索模块"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化知识检索模块
        
        Args:
            config: 检索配置
        """
        self.config = config
        self.vector_dim = config.get("dimension", 768)
        self.top_k = config.get("top_k", 5)
        self.similarity_threshold = config.get("similarity_threshold", 0.5)
        
        # 知识条目存储
        self.knowledge_entries = []
        self.knowledge_vectors = []
        
        # 加载知识库
        self._load_knowledge_base()
    
    def _load_knowledge_base(self):
        """加载知识库"""
        kb_path = "knowledge_base"
        if not os.path.exists(kb_path):
            os.makedirs(kb_path, exist_ok=True)
            # 创建示例知识库
            self._create_sample_knowledge()
            return
        
        # 加载知识条目
        kb_file = os.path.join(kb_path, "knowledge_entries.json")
        if os.path.exists(kb_file):
            with open(kb_file, 'r', encoding='utf-8') as f:
                self.knowledge_entries = json.load(f)
        
        # 加载向量
        vector_file = os.path.join(kb_path, "knowledge_vectors.npy")
        if os.path.exists(vector_file):
            self.knowledge_vectors = np.load(vector_file).tolist()
        
        # 如果没有知识，创建示例
        if not self.knowledge_entries:
            self._create_sample_knowledge()
    
    def _create_sample_knowledge(self):
        """创建示例知识库"""
        sample_knowledge = [
            {
                "id": 0,
                "query": "什么是人工智能",
                "answer": "人工智能(AI)是计算机科学的一个分支，致力于创建能够模拟人类智能的机器和系统。",
                "metadata": {"category": "AI", "difficulty": "basic"}
            },
            {
                "id": 1,
                "query": "机器学习是什么",
                "answer": "机器学习是人工智能的一个子领域，通过算法让计算机从数据中学习规律，无需明确编程。",
                "metadata": {"category": "ML", "difficulty": "basic"}
            },
            {
                "id": 2,
                "query": "深度学习的特点",
                "answer": "深度学习使用多层神经网络来学习数据的复杂模式，在图像识别、自然语言处理等领域表现出色。",
                "metadata": {"category": "DL", "difficulty": "intermediate"}
            },
            {
                "id": 3,
                "query": "Python编程语言",
                "answer": "Python是一种高级编程语言，语法简洁，广泛用于数据科学、机器学习和Web开发。",
                "metadata": {"category": "Programming", "difficulty": "basic"}
            },
            {
                "id": 4,
                "query": "如何学习编程",
                "answer": "学习编程建议从基础语法开始，多做练习项目，参与开源项目，持续实践和学习新技术。",
                "metadata": {"category": "Learning", "difficulty": "basic"}
            }
        ]
        
        self.knowledge_entries = sample_knowledge
        
        # 为每个知识条目生成随机向量（实际应用中应该用真实的语义向量）
        self.knowledge_vectors = []
        for entry in sample_knowledge:
            vector = np.random.normal(0, 0.1, self.vector_dim)
            self.knowledge_vectors.append(vector.tolist())
        
        # 保存知识库
        self._save_knowledge_base()
    
    def _save_knowledge_base(self):
        """保存知识库"""
        kb_path = "knowledge_base"
        if not os.path.exists(kb_path):
            os.makedirs(kb_path, exist_ok=True)
        
        # 保存知识条目
        kb_file = os.path.join(kb_path, "knowledge_entries.json")
        with open(kb_file, 'w', encoding='utf-8') as f:
            json.dump(self.knowledge_entries, f, ensure_ascii=False, indent=2)
        
        # 保存向量
        if self.knowledge_vectors:
            vector_file = os.path.join(kb_path, "knowledge_vectors.npy")
            np.save(vector_file, np.array(self.knowledge_vectors))
    
    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def retrieve(self, query_vector: np.ndarray) -> List[Dict[str, Any]]:
        """
        检索相关知识
        
        Args:
            query_vector: 查询向量
            
        Returns:
            检索到的知识条目列表
        """
        if not self.knowledge_vectors:
            return []
        
        # 计算与所有知识向量的相似度
        similarities = []
        for i, kb_vector in enumerate(self.knowledge_vectors):
            kb_vector = np.array(kb_vector)
            similarity = self._cosine_similarity(query_vector, kb_vector)
            similarities.append((similarity, i))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[0], reverse=True)
        
        # 选择top_k个结果
        results = []
        for similarity, idx in similarities[:self.top_k]:
            if similarity >= self.similarity_threshold:
                entry = self.knowledge_entries[idx].copy()
                entry["score"] = similarity
                results.append(entry)
        
        return results
    
    def add_knowledge(self, query: str, answer: str, query_vector: np.ndarray, 
                     metadata: Optional[Dict[str, Any]] = None):
        """添加新知识"""
        entry = {
            "id": len(self.knowledge_entries),
            "query": query,
            "answer": answer,
            "metadata": metadata or {}
        }
        
        self.knowledge_entries.append(entry)
        self.knowledge_vectors.append(query_vector.tolist())
        
        # 保存更新后的知识库
        self._save_knowledge_base()