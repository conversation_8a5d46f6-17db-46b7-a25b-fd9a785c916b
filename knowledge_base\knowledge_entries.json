[{"id": 0, "query": "什么是人工智能", "answer": "人工智能(AI)是计算机科学的一个分支，致力于创建能够模拟人类智能的机器和系统。", "metadata": {"category": "AI", "difficulty": "basic"}}, {"id": 1, "query": "机器学习是什么", "answer": "机器学习是人工智能的一个子领域，通过算法让计算机从数据中学习规律，无需明确编程。", "metadata": {"category": "ML", "difficulty": "basic"}}, {"id": 2, "query": "深度学习的特点", "answer": "深度学习使用多层神经网络来学习数据的复杂模式，在图像识别、自然语言处理等领域表现出色。", "metadata": {"category": "DL", "difficulty": "intermediate"}}, {"id": 3, "query": "Python编程语言", "answer": "Python是一种高级编程语言，语法简洁，广泛用于数据科学、机器学习和Web开发。", "metadata": {"category": "Programming", "difficulty": "basic"}}, {"id": 4, "query": "如何学习编程", "answer": "学习编程建议从基础语法开始，多做练习项目，参与开源项目，持续实践和学习新技术。", "metadata": {"category": "Learning", "difficulty": "basic"}}]