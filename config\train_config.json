{"data": {"train_file": "data/processed/train.json", "valid_file": "data/processed/valid.json", "test_file": "data/processed/test.json", "knowledge_base_path": "knowledge_base/", "batch_size": 16, "num_workers": 4}, "training": {"epochs": 10, "learning_rate": 5e-05, "warmup_steps": 500, "weight_decay": 0.01, "gradient_accumulation_steps": 2, "max_grad_norm": 1.0, "mixed_precision": "fp16", "save_steps": 1000, "logging_steps": 100}, "evaluation": {"eval_steps": 1000, "metrics": ["bleu", "rouge", "accuracy", "f1"]}, "output": {"output_dir": "models/saved/", "log_dir": "logs/"}}