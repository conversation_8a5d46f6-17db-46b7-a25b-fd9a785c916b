#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版语义理解模型
不依赖重型库，使用基础方法实现
"""

import numpy as np
import jieba
from typing import Dict, List, Tuple, Any, Optional
import json
import os

class SemanticModel:
    """简化版语义理解模型"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化语义理解模型
        
        Args:
            config: 模型配置
        """
        self.config = config
        self.vocabulary = {}
        self.word_vectors = {}
        self.embedding_dim = 768
        
        # 创建基础词汇表
        self._build_vocabulary()
        
    def _build_vocabulary(self):
        """构建基础词汇表"""
        # 基础词汇
        basic_words = [
            "你好", "问题", "回答", "知识", "信息", "系统", "帮助", 
            "什么", "怎么", "为什么", "哪里", "什么时候", "如何",
            "是", "的", "了", "和", "或", "但是", "因为", "所以",
            "请", "谢谢", "对不起", "再见", "开始", "结束"
        ]
        
        for i, word in enumerate(basic_words):
            self.vocabulary[word] = i
            # 随机初始化词向量
            self.word_vectors[word] = np.random.normal(0, 0.1, self.embedding_dim)
    
    def encode(self, text: str) -> np.ndarray:
        """
        编码文本为语义向量
        
        Args:
            text: 输入文本
            
        Returns:
            语义向量
        """
        # 分词
        words = list(jieba.cut(text))
        
        # 计算文本向量（词向量平均）
        vectors = []
        for word in words:
            if word in self.word_vectors:
                vectors.append(self.word_vectors[word])
            else:
                # 未知词使用随机向量
                vectors.append(np.random.normal(0, 0.1, self.embedding_dim))
        
        if vectors:
            text_vector = np.mean(vectors, axis=0)
        else:
            text_vector = np.zeros(self.embedding_dim)
            
        return text_vector
    
    def batch_encode(self, texts: List[str]) -> np.ndarray:
        """
        批量编码文本
        
        Args:
            texts: 文本列表
            
        Returns:
            批量语义向量
        """
        vectors = []
        for text in texts:
            vectors.append(self.encode(text))
        return np.array(vectors)
    
    def predict_intent(self, text: str) -> str:
        """
        预测文本意图（简化版）
        
        Args:
            text: 输入文本
            
        Returns:
            意图类别
        """
        # 简单的关键词匹配
        text_lower = text.lower()
        
        if any(word in text for word in ["什么", "啥", "?"]):
            return "question"
        elif any(word in text for word in ["你好", "hi", "hello"]):
            return "greeting"
        elif any(word in text for word in ["谢谢", "感谢"]):
            return "thanks"
        elif any(word in text for word in ["再见", "拜拜", "bye"]):
            return "goodbye"
        else:
            return "general"