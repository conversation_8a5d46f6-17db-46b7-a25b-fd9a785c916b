import json
import os
from typing import Dict, Any

def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    if not os.path.exists(config_path):
        print(f"警告: 配置文件 {config_path} 不存在，使用默认配置")
        return {
            "semantic_model": {
                "model_name": "bert-base-chinese",
                "max_seq_length": 128,
                "hidden_size": 768
            },
            "retrieval_model": {
                "dimension": 768,
                "top_k": 5,
                "similarity_threshold": 0.0
            },
            "context_model": {
                "max_history_turns": 5
            },
            "generation_model": {
                "model_name": "fnlp/bart-base-chinese",
                "max_length": 512
            }
        }
    
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

class Config:
    """配置加载和管理类"""
    
    def __init__(self, config_path: str):
        """
        初始化配置对象
        
        Args:
            config_path: 配置文件路径
        """
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
            
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 "model.hidden_size"
            default: 默认值，当键不存在时返回
            
        Returns:
            配置值
        """
        if '.' not in key:
            return self.config.get(key, default)
            
        # 处理嵌套键
        keys = key.split('.')
        value = self.config
        for k in keys:
            if not isinstance(value, dict) or k not in value:
                return default
            value = value[k]
        return value
    
    def get_all(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            所有配置的字典
        """
        return self.config.copy()
    
    def __getitem__(self, key: str) -> Any:
        """
        使配置对象可以通过字典方式访问
        
        Args:
            key: 配置键
            
        Returns:
            配置值
        """
        return self.get(key)
    
    def __contains__(self, key: str) -> bool:
        """
        检查配置键是否存在
        
        Args:
            key: 配置键
            
        Returns:
            是否存在
        """
        return key in self.config 